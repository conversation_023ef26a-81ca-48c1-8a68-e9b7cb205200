# 🚀 Hướng Dẫn Deployment - Shopping Demo

## ✅ Tình Trạng Hiện Tại

- ✅ Database: MariaDB đang chạy
- ✅ Kết nối: Username `root`, Password `root` 
- ✅ Dữ liệu: 25 sản phẩm đã được thêm vào database `shoppingdb`
- ✅ Build: WAR file đã được tạo thành công

## 🎯 Các Bước Deployment

### Bước 1: Kiểm tra Database (Đã hoàn thành ✅)
```bash
# Database đã sẵn sàng với 25 sản phẩm
mysql -u root -proot -e "USE shoppingdb; SELECT COUNT(*) FROM product;"
```

### Bước 2: Deploy WAR file
```bash
# Copy WAR file vào Tomcat webapps
cp target/demo-1.0-SNAPSHOT.war $TOMCAT_HOME/webapps/

# Hoặc nếu bạn dùng Tomcat local:
cp target/demo-1.0-SNAPSHOT.war /path/to/tomcat/webapps/
```

### Bước 3: Start Tomcat
```bash
# Start Tomcat server
$TOMCAT_HOME/bin/startup.sh

# Hoặc trên Windows:
# %TOMCAT_HOME%\bin\startup.bat
```

### Bước 4: Truy cập ứng dụng
- **Trang chủ:** `http://localhost:8080/demo-1.0-SNAPSHOT/`
- **Danh sách sản phẩm:** `http://localhost:8080/demo-1.0-SNAPSHOT/products`

## 🔧 Cấu Hình Database

File `src/main/webapp/WEB-INF/context.xml` đã được cấu hình:
```xml
<Resource name="jdbc/shopping" auth="Container" type="javax.sql.DataSource"
          username="root" password="root" 
          url="****************************************"/>
```

## 📊 Sample Data

Database hiện có 25 sản phẩm bao gồm:
- 💻 **Laptops:** MacBook Pro, Dell XPS, HP Spectre, Lenovo ThinkPad, ASUS ROG
- 📱 **Mobile:** iPhone 15 Pro Max, Samsung Galaxy S24, Google Pixel 8, OnePlus 12, iPad Pro
- 🎧 **Audio:** Sony WH-1000XM5, AirPods Pro, Bose QuietComfort, Sennheiser, JBL
- 🎮 **Gaming:** PlayStation 5, Xbox Series X, Nintendo Switch, Steam Deck, Razer Mouse
- ⌨️ **Accessories:** Magic Keyboard, Logitech Mouse, Samsung Monitor, Anker PowerCore, USB-C Hub

## 🛠 Troubleshooting

### Nếu không thấy sản phẩm:
1. **Kiểm tra Tomcat logs:**
   ```bash
   tail -f $TOMCAT_HOME/logs/catalina.out
   ```

2. **Kiểm tra database connection:**
   ```bash
   mysql -u root -proot -e "USE shoppingdb; SELECT COUNT(*) FROM product;"
   ```

3. **Restart Tomcat:**
   ```bash
   $TOMCAT_HOME/bin/shutdown.sh
   $TOMCAT_HOME/bin/startup.sh
   ```

### Nếu có lỗi database:
- Đảm bảo MariaDB đang chạy: `brew services list | grep mariadb`
- Kiểm tra credentials trong `context.xml`
- Restart Tomcat sau khi thay đổi cấu hình

## 🎉 Kết Quả Mong Đợi

Khi truy cập `/products`, bạn sẽ thấy:
- Grid layout với 25 sản phẩm
- Hình ảnh sản phẩm từ Unsplash
- Tên và giá sản phẩm
- Responsive design
- Thông báo "Showing 25 products from database"

## 📝 Ghi Chú

- Ứng dụng sử dụng JNDI DataSource để kết nối database
- Tất cả hình ảnh được load từ Unsplash CDN
- JSP sử dụng JSTL tags để render dữ liệu
- Error handling đã được implement để hiển thị thông báo lỗi rõ ràng
