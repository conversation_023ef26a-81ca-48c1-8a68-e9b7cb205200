# Quick Start Guide - Shopping Demo

## 🚀 Fastest Way to Test (No Database Required)

1. **Build the application:**
   ```bash
   ./mvnw clean package
   ```

2. **Deploy to Tomcat:**
   - Copy `target/demo-1.0-SNAPSHOT.war` to your Tomcat `webapps` folder
   - Start Tomcat

3. **Test immediately:**
   - Visit: `http://localhost:8080/demo-1.0-SNAPSHOT/`
   - Click "View Products (Demo Data)" 
   - ✅ See 20 sample products with real images!

## 📊 With Database (Full Experience)

### Step 1: Database Setup
```bash
# Start MariaDB/MySQL
mysql -u root -p

# Run one of these scripts:
mysql -u root -p < database-setup.sql        # Basic data (20 products)
mysql -u root -p < extended-sample-data.sql  # Extended data (50+ products)
```

### Step 2: Test Database Version
- Visit: `http://localhost:8080/demo-1.0-SNAPSHOT/`
- Click "View Products (Database)"
- ✅ See products from your database!

## 🎯 What You'll See

### Demo Data Version (`/mock-products`)
- 20 carefully curated products
- Real product images from Unsplash
- Categories: Electronics, Gaming, Audio, Accessories
- Works instantly - no setup required

### Database Version (`/products`)
- Basic setup: 20 products with realistic data
- Extended setup: 50+ products across 9 categories:
  - 💻 Electronics (Computers, Mobile, Audio, Gaming)
  - 🏠 Home & Kitchen
  - 🏃 Sports & Fitness  
  - 👕 Fashion & Accessories
  - 📚 Books & Education
  - 📷 Photography & Cameras

## 🛠 Troubleshooting

### "No products available"
- **For `/mock-products`:** Check if WAR deployed correctly
- **For `/products`:** Verify database connection and data

### Database connection issues
1. Check MariaDB is running: `systemctl status mariadb`
2. Verify credentials in `src/main/webapp/WEB-INF/context.xml`
3. Test connection: `mysql -u root -p -h localhost shoppingdb`

### Images not loading
- Demo uses Unsplash CDN - requires internet connection
- Images have fallback to placeholder if unavailable

## 📱 Features to Test

1. **Responsive Design:** Try different screen sizes
2. **Image Fallback:** Disable internet to see placeholder images
3. **Navigation:** Test back/forward between pages
4. **Product Grid:** Notice the hover effects and layout
5. **JSTL Integration:** View page source to see clean HTML output

## 🔧 Development Notes

- **Mock Controller:** `MockProductController.java` - No database dependency
- **Database Controller:** `ProductController.java` - Uses JNDI DataSource
- **Shared View:** Both controllers use the same JSP template
- **Sample Data:** Real product names and prices for realistic testing

## 📈 Next Steps

1. **Add CRUD Operations:** Create, Update, Delete products
2. **Add Categories:** Product categorization and filtering
3. **Add Search:** Product search functionality
4. **Add Shopping Cart:** Session-based cart management
5. **Add User Authentication:** Login/logout functionality

---

**💡 Pro Tip:** Start with the demo data version to quickly see the application working, then set up the database for the full experience!
