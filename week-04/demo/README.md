# Shopping Demo Web Application

A simple Jakarta EE web application demonstrating product management using servlets, JSP, and JSTL.

## Features

- Product listing with responsive design
- Database connectivity using DataSource
- Modern JSP with JSTL tags
- Clean MVC architecture

## Prerequisites

- Java 22 or higher
- MariaDB/MySQL database server
- Apache Tomcat 10.1+ or similar Jakarta EE 10 compatible server
- Maven 3.6+

## Database Setup

### Option 1: Basic Sample Data
1. Install and start MariaDB/MySQL
2. Run the basic database setup script:
   ```sql
   mysql -u root -p < database-setup.sql
   ```

### Option 2: Extended Sample Data (Recommended)
For a more comprehensive demo with 50+ products across multiple categories:
   ```sql
   mysql -u root -p < extended-sample-data.sql
   ```

### Quick Setup Script
For automated database setup, run:
   ```bash
   ./init-database.sh
   ```

## Configuration

The application is configured to connect to:
- Database: `shoppingdb`
- Host: `localhost:3306`
- Username: `root`
- Password: `123456`

To change these settings, edit `src/main/webapp/WEB-INF/context.xml`.

## Building and Running

1. **Build the application:**
   ```bash
   ./mvnw clean package
   ```

2. **Deploy to Tomcat:**
   - Copy `target/demo-1.0-SNAPSHOT.war` to your Tomcat `webapps` directory
   - Start Tomcat
   - Access the application at: `http://localhost:8080/demo-1.0-SNAPSHOT/`

## Application Structure

```
src/main/java/iuh/fit/se/
├── controller/
│   └── ProductController.java    # Servlet handling product requests
├── dao/
│   ├── ProductDAO.java          # Data access interface
│   └── Impl/
│       └── ProductDAOImpl.java  # Database implementation
└── entity/
    └── Product.java             # Product entity class

src/main/webapp/
├── index.jsp                    # Home page
├── views/product/
│   └── index.jsp               # Product listing page
└── WEB-INF/
    ├── web.xml                 # Web application configuration
    └── context.xml             # Database connection configuration
```

## Usage

### Available Endpoints

1. **Home Page:** `/` - Welcome page with database information
2. **Product List:** `/products` - Products from database

### Quick Start Guide

1. **Setup Database:** Run database setup script or manual SQL commands
2. **Deploy Application:** Copy WAR file to Tomcat webapps directory
3. **Access Products:** Visit `/products` to see your product catalog
4. **Navigation:** Use the "Back to Home" link to return to the main page

### Sample Data Categories

The extended sample data includes:
- **Electronics:** Laptops, phones, tablets, gaming devices
- **Audio:** Headphones, speakers, earbuds
- **Accessories:** Keyboards, mice, monitors, chargers
- **Smart Devices:** Watches, smart home devices
- **Photography:** Cameras, drones, action cameras
- **Home & Kitchen:** Appliances, cleaning devices
- **Sports & Fitness:** Exercise equipment, wearables
- **Fashion:** Shoes, clothing, sunglasses
- **Books & Education:** Technical books, e-readers

## Technologies Used

- **Jakarta EE 10:** Servlets, JSP, JSTL
- **Java 22:** Latest Java features with Lombok
- **MariaDB:** Database storage
- **Maven:** Build and dependency management
- **Bootstrap-style CSS:** Responsive design

## Troubleshooting

### Common Issues

1. **Database Connection Error:**
   - Verify MariaDB is running
   - Check database credentials in `context.xml`
   - Ensure `shoppingdb` database exists

2. **JSP Compilation Error:**
   - Verify all JSTL dependencies are included
   - Check JSP syntax in view files

3. **404 Error:**
   - Verify the WAR file is properly deployed
   - Check servlet URL mappings
   - Ensure Tomcat is running

### Development Notes

- The application uses JNDI DataSource for database connections
- Lombok is used for reducing boilerplate code in entities
- The application follows MVC pattern with clear separation of concerns
- All JSP files use modern JSTL syntax for better maintainability
