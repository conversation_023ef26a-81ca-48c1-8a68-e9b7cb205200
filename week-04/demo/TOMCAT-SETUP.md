# 🚀 Hướng Dẫn Setup Tomcat - Shopping Demo

## ✅ Đã Hoàn Thành

1. ✅ **MariaDB Driver:** Đ<PERSON> copy vào `$TOMCAT_HOME/lib/`
2. ✅ **JNDI Configuration:** Đã tạo file `demo.xml` trong `conf/Catalina/localhost/`
3. ✅ **Web.xml:** Đã thêm resource-ref
4. ✅ **WAR File:** Đã build thành công

## 🔧 Các <PERSON>c Đã Thực Hiện

### 1. Copy MariaDB Driver
```bash
# Driver đã được copy vào:
/Users/<USER>/Desktop/www/apache-tomcat-11.0.10/lib/mariadb-java-client-3.5.1.jar
```

### 2. Tạo JNDI Configuration
File: `/Users/<USER>/Desktop/www/apache-tomcat-11.0.10/conf/Catalina/localhost/demo.xml`
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Context>
    <Resource name="jdbc/shopping" 
              auth="Container" 
              type="javax.sql.DataSource"
              maxTotal="100" 
              maxIdle="30" 
              maxWaitMillis="10000"
              username="root" 
              password="root" 
              driverClassName="org.mariadb.jdbc.Driver"
              url="****************************************" 
              validationQuery="SELECT 1"
              testOnBorrow="true" />
</Context>
```

### 3. Cập nhật web.xml
Đã thêm resource-ref để khai báo JNDI resource.

## 🚀 Deployment Steps

### Bước 1: Undeploy ứng dụng cũ (nếu có)
Trong IntelliJ IDEA:
- Stop server nếu đang chạy
- Undeploy artifact `demo:war exploded`

### Bước 2: Deploy WAR file mới
```bash
# Copy WAR file mới
cp target/demo-1.0-SNAPSHOT.war /Users/<USER>/Desktop/www/apache-tomcat-11.0.10/webapps/
```

### Bước 3: Restart Tomcat
```bash
# Stop Tomcat
/Users/<USER>/Desktop/www/apache-tomcat-11.0.10/bin/shutdown.sh

# Start Tomcat
/Users/<USER>/Desktop/www/apache-tomcat-11.0.10/bin/startup.sh
```

### Bước 4: Kiểm tra logs
```bash
# Theo dõi logs
tail -f /Users/<USER>/Desktop/www/apache-tomcat-11.0.10/logs/catalina.out
```

## 🎯 Kết Quả Mong Đợi

Khi truy cập `http://localhost:8080/demo-1.0-SNAPSHOT/products`, bạn sẽ thấy:

```
✅ ProductController initialized successfully with database connection
🔍 ProductController: Loading products from database...
✅ Successfully loaded 25 products from database
```

## 🛠 Troubleshooting

### Nếu vẫn gặp lỗi "No suitable driver":
1. **Kiểm tra driver trong Tomcat lib:**
   ```bash
   ls -la /Users/<USER>/Desktop/www/apache-tomcat-11.0.10/lib/mariadb*
   ```

2. **Kiểm tra JNDI config:**
   ```bash
   cat /Users/<USER>/Desktop/www/apache-tomcat-11.0.10/conf/Catalina/localhost/demo.xml
   ```

3. **Restart Tomcat hoàn toàn:**
   ```bash
   # Kill tất cả process Tomcat
   pkill -f tomcat
   
   # Start lại
   /Users/<USER>/Desktop/www/apache-tomcat-11.0.10/bin/startup.sh
   ```

### Nếu vẫn gặp lỗi JNDI:
1. **Kiểm tra tên context:** Đảm bảo file `demo.xml` khớp với tên WAR
2. **Kiểm tra resource name:** `jdbc/shopping` phải khớp trong tất cả file
3. **Xem Tomcat logs:** Tìm lỗi JNDI trong catalina.out

## 📝 Alternative: Deploy qua IntelliJ

Nếu bạn muốn deploy qua IntelliJ:
1. **Artifact Configuration:** Chọn `demo:war exploded`
2. **Application Context:** Để trống hoặc `/demo`
3. **Deploy:** Click Deploy trong Run Configuration

## 🎉 Test Thành Công

Khi setup đúng, bạn sẽ thấy:
- Trang chủ hiển thị thông tin database
- `/products` hiển thị 25 sản phẩm với hình ảnh
- Console logs hiển thị kết nối database thành công
- Không có lỗi SQLException trong logs
