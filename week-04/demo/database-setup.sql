-- Database setup script for Shopping Demo
-- Run this script in MariaDB/MySQL to create the database and sample data

-- Create database
CREATE DATABASE IF NOT EXISTS shoppingdb;
USE shoppingdb;

-- Create product table
CREATE TABLE IF NOT EXISTS product (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    image VARCHAR(500)
);

-- Insert sample data
INSERT INTO product (name, price, image) VALUES
('Laptop Dell XPS 13', 1299.99, 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=300&h=200&fit=crop'),
('iPhone 15 Pro', 999.99, 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=300&h=200&fit=crop'),
('Samsung Galaxy S24', 899.99, 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=200&fit=crop'),
('MacBook Air M2', 1199.99, 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=300&h=200&fit=crop'),
('iPad Pro 12.9"', 1099.99, 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=300&h=200&fit=crop'),
('Sony WH-1000XM5 Headphones', 399.99, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=200&fit=crop'),
('Nintendo Switch OLED', 349.99, 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=300&h=200&fit=crop'),
('AirPods Pro 2nd Gen', 249.99, 'https://images.unsplash.com/photo-1600294037681-c80b4cb5b434?w=300&h=200&fit=crop'),
('Samsung 32" 4K Monitor', 299.99, 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=300&h=200&fit=crop'),
('Logitech MX Master 3S', 99.99, 'https://images.unsplash.com/photo-1527814050087-3793815479db?w=300&h=200&fit=crop'),
('Apple Magic Keyboard', 179.99, 'https://images.unsplash.com/photo-1587829741301-dc798b83add3?w=300&h=200&fit=crop'),
('Canon EOS R5 Camera', 3899.99, 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=300&h=200&fit=crop'),
('Sony PlayStation 5', 499.99, 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=300&h=200&fit=crop'),
('Microsoft Surface Pro 9', 1299.99, 'https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?w=300&h=200&fit=crop'),
('Apple Watch Series 9', 399.99, 'https://images.unsplash.com/photo-1434493789847-2f02dc6ca35d?w=300&h=200&fit=crop'),
('Bose QuietComfort 45', 329.99, 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=300&h=200&fit=crop'),
('Tesla Model S Plaid Toy', 29.99, 'https://images.unsplash.com/photo-1560958089-b8a1929cea89?w=300&h=200&fit=crop'),
('Gaming Mechanical Keyboard', 149.99, 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=300&h=200&fit=crop'),
('Wireless Charging Pad', 39.99, 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=200&fit=crop'),
('USB-C Hub 7-in-1', 79.99, 'https://images.unsplash.com/photo-1625842268584-8f3296236761?w=300&h=200&fit=crop');

-- Verify data
SELECT * FROM product;
