#!/bin/bash

# Database initialization script for Shopping Demo
# This script sets up the MariaDB/MySQL database with sample data

echo "🚀 Initializing Shopping Demo Database..."

# Check if MySQL/MariaDB is running
if ! command -v mysql &> /dev/null; then
    echo "❌ MySQL/MariaDB client not found. Please install MySQL or MariaDB."
    exit 1
fi

# Database configuration
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="shoppingdb"
DB_USER="root"

echo "📊 Setting up database: $DB_NAME"
echo "🔗 Host: $DB_HOST:$DB_PORT"
echo "👤 User: $DB_USER"
echo ""

# Prompt for password
read -s -p "🔐 Enter MySQL root password: " DB_PASSWORD
echo ""

# Test connection
echo "🔍 Testing database connection..."
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &> /dev/null

if [ $? -ne 0 ]; then
    echo "❌ Failed to connect to database. Please check your credentials and ensure MySQL/MariaDB is running."
    exit 1
fi

echo "✅ Database connection successful!"

# Create database and run setup script
echo "📝 Creating database and tables..."
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" < database-setup.sql

if [ $? -eq 0 ]; then
    echo "✅ Database setup completed successfully!"
    echo ""
    echo "📊 Database Summary:"
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SELECT COUNT(*) as 'Total Products' FROM product;"
    echo ""
    echo "🎉 Your Shopping Demo is ready!"
    echo "📱 Deploy the WAR file and visit: http://localhost:8080/demo-1.0-SNAPSHOT/products"
else
    echo "❌ Database setup failed. Please check the error messages above."
    exit 1
fi
