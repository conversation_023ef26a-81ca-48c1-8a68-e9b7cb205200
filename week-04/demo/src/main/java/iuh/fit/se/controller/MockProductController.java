package iuh.fit.se.controller;

import iuh.fit.se.dao.Impl.MockProductDAOImpl;
import iuh.fit.se.dao.ProductDAO;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

/**
 * Mock Product Controller for testing without database
 * This controller uses mock data instead of database connection
 * Access via: /mock-products
 */
@WebServlet(name = "mockProductController", urlPatterns = "/mock-products")
public class MockProductController extends HttpServlet {
    private ProductDAO productDAO;
    
    @Override
    public void init() throws ServletException {
        super.init();
        // Use mock implementation instead of database
        this.productDAO = new MockProductDAOImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws
            ServletException, IOException {
        req.setAttribute("products", productDAO.findAll());
        req.getRequestDispatcher("views/product/index.jsp").forward(req, resp);
    }
}
