package iuh.fit.se.dao.Impl;

import iuh.fit.se.dao.ProductDAO;
import iuh.fit.se.entity.Product;

import java.util.ArrayList;
import java.util.List;

/**
 * Mock implementation of ProductDAO for testing without database
 * This class provides sample data for demonstration purposes
 */
public class MockProductDAOImpl implements ProductDAO {
    
    @Override
    public List<Product> findAll() {
        List<Product> products = new ArrayList<>();
        
        // Add sample products with real images from Unsplash
        products.add(new Product(1, "Laptop Dell XPS 13", 1299.99, 
            "https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=300&h=200&fit=crop"));
        
        products.add(new Product(2, "iPhone 15 Pro", 999.99, 
            "https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=300&h=200&fit=crop"));
        
        products.add(new Product(3, "Samsung Galaxy S24", 899.99, 
            "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=200&fit=crop"));
        
        products.add(new Product(4, "MacBook Air M2", 1199.99, 
            "https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=300&h=200&fit=crop"));
        
        products.add(new Product(5, "iPad Pro 12.9\"", 1099.99, 
            "https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=300&h=200&fit=crop"));
        
        products.add(new Product(6, "Sony WH-1000XM5 Headphones", 399.99, 
            "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=200&fit=crop"));
        
        products.add(new Product(7, "Nintendo Switch OLED", 349.99, 
            "https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=300&h=200&fit=crop"));
        
        products.add(new Product(8, "AirPods Pro 2nd Gen", 249.99, 
            "https://images.unsplash.com/photo-1600294037681-c80b4cb5b434?w=300&h=200&fit=crop"));
        
        products.add(new Product(9, "Samsung 32\" 4K Monitor", 299.99, 
            "https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=300&h=200&fit=crop"));
        
        products.add(new Product(10, "Logitech MX Master 3S", 99.99, 
            "https://images.unsplash.com/photo-1527814050087-3793815479db?w=300&h=200&fit=crop"));
        
        products.add(new Product(11, "Apple Magic Keyboard", 179.99, 
            "https://images.unsplash.com/photo-1587829741301-dc798b83add3?w=300&h=200&fit=crop"));
        
        products.add(new Product(12, "Canon EOS R5 Camera", 3899.99, 
            "https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=300&h=200&fit=crop"));
        
        products.add(new Product(13, "Sony PlayStation 5", 499.99, 
            "https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=300&h=200&fit=crop"));
        
        products.add(new Product(14, "Microsoft Surface Pro 9", 1299.99, 
            "https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?w=300&h=200&fit=crop"));
        
        products.add(new Product(15, "Apple Watch Series 9", 399.99, 
            "https://images.unsplash.com/photo-1434493789847-2f02dc6ca35d?w=300&h=200&fit=crop"));
        
        products.add(new Product(16, "Bose QuietComfort 45", 329.99, 
            "https://images.unsplash.com/photo-1583394838336-acd977736f90?w=300&h=200&fit=crop"));
        
        products.add(new Product(17, "Gaming Mechanical Keyboard", 149.99, 
            "https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=300&h=200&fit=crop"));
        
        products.add(new Product(18, "Wireless Charging Pad", 39.99, 
            "https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=300&h=200&fit=crop"));
        
        products.add(new Product(19, "USB-C Hub 7-in-1", 79.99, 
            "https://images.unsplash.com/photo-1625842268584-8f3296236761?w=300&h=200&fit=crop"));
        
        products.add(new Product(20, "Bluetooth Speaker", 89.99, 
            "https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=300&h=200&fit=crop"));
        
        return products;
    }
}
