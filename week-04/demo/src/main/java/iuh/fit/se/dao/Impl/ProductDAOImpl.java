package iuh.fit.se.dao.Impl;

import iuh.fit.se.dao.ProductDAO;
import iuh.fit.se.entity.Product;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ProductDAOImpl implements ProductDAO {
    private final DataSource datasource;
    public ProductDAOImpl(DataSource datasource) {
        this.datasource = datasource;
    }
    @Override
    public List<Product> findAll() {
        String sql = "SELECT * FROM product";
        List<Product> list = new ArrayList<>();

        try (Connection con = this.datasource.getConnection();
             PreparedStatement ps = con.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()) {

            while (rs.next()) {
                int id = rs.getInt("id");
                String name = rs.getString("name");
                double price = rs.getDouble("price");
                String image = rs.getString("image");
                list.add(new Product(id, name, price, image));
            }

            System.out.println("✅ Successfully loaded " + list.size() + " products from database");

        } catch (SQLException e) {
            System.err.println("❌ Database error in ProductDAOImpl.findAll():");
            System.err.println("   Error Code: " + e.getErrorCode());
            System.err.println("   SQL State: " + e.getSQLState());
            System.err.println("   Message: " + e.getMessage());
            e.printStackTrace();

            // Return empty list instead of null to prevent JSP errors
            System.err.println("⚠️  Returning empty product list due to database error");
        }

        return list;
    }
}
