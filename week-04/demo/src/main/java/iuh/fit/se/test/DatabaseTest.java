package iuh.fit.se.test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Simple database connection test
 * Run this to verify database connectivity
 */
public class DatabaseTest {
    private static final String URL = "****************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "root";
    
    public static void main(String[] args) {
        System.out.println("🔍 Testing database connection...");
        
        try {
            // Load MariaDB driver
            Class.forName("org.mariadb.jdbc.Driver");
            System.out.println("✅ MariaDB driver loaded successfully");
            
            // Test connection
            try (Connection conn = DriverManager.getConnection(URL, USERNAME, PASSWORD)) {
                System.out.println("✅ Database connection successful!");
                
                // Test query
                String sql = "SELECT COUNT(*) as total FROM product";
                try (PreparedStatement ps = conn.prepareStatement(sql);
                     ResultSet rs = ps.executeQuery()) {
                    
                    if (rs.next()) {
                        int total = rs.getInt("total");
                        System.out.println("📊 Total products in database: " + total);
                    }
                }
                
                // Show first 3 products
                sql = "SELECT id, name, price FROM product ORDER BY id LIMIT 3";
                try (PreparedStatement ps = conn.prepareStatement(sql);
                     ResultSet rs = ps.executeQuery()) {
                    
                    System.out.println("\n📦 Sample products:");
                    while (rs.next()) {
                        System.out.printf("   %d. %s - $%.2f%n", 
                            rs.getInt("id"), 
                            rs.getString("name"), 
                            rs.getDouble("price"));
                    }
                }
                
                System.out.println("\n🎉 Database test completed successfully!");
                
            }
        } catch (ClassNotFoundException e) {
            System.err.println("❌ MariaDB driver not found: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("❌ Database error: " + e.getMessage());
            System.err.println("   Error Code: " + e.getErrorCode());
            System.err.println("   SQL State: " + e.getSQLState());
        }
    }
}
