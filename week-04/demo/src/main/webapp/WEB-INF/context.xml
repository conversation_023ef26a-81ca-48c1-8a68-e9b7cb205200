<Context>
    <Resource name="jdbc/shopping"
              auth="Container"
              type="javax.sql.DataSource"
              maxTotal="100"
              maxIdle="30"
              maxWaitMillis="10000"
              username="root"
              password="root"
              driverClassName="org.mariadb.jdbc.Driver"
              url="****************************************"
              validationQuery="SELECT 1"
              testOnBorrow="true"
              removeAbandonedOnBorrow="true"
              removeAbandonedTimeout="60" />
</Context>