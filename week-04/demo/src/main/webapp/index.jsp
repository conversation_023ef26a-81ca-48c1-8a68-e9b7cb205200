<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html>
<head>
    <title>Shopping Demo - Home</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .welcome-text {
            color: #666;
            margin-bottom: 30px;
            font-size: 18px;
        }
        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        .nav-link {
            display: inline-block;
            padding: 12px 24px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .nav-link:hover {
            background-color: #2980b9;
        }
        .nav-link.primary {
            background-color: #e74c3c;
        }
        .nav-link.primary:hover {
            background-color: #c0392b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Welcome to Shopping Demo</h1>
        <p class="welcome-text">
            This is a simple web application demonstrating product management using Jakarta EE technologies.
        </p>

        <div class="nav-links">
            <a href="${pageContext.request.contextPath}/products" class="nav-link primary">View Products</a>
        </div>
    </div>
</body>
</html>