<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="jakarta.tags.core" %>
<!DOCTYPE html>
<html>
<head>
    <title>Product List</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .product-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .product-price {
            font-size: 16px;
            color: #e74c3c;
            font-weight: bold;
        }
        .no-products {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 40px;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
        }
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="${pageContext.request.contextPath}/" class="back-link">← Back to Home</a>
        
        <h1>Product Catalog</h1>
        
        <c:choose>
            <c:when test="${not empty products}">
                <div class="product-grid">
                    <c:forEach var="product" items="${products}">
                        <div class="product-card">
                            <c:choose>
                                <c:when test="${not empty product.image}">
                                    <img src="${product.image}" alt="${product.name}" class="product-image" 
                                         onerror="this.src='https://via.placeholder.com/300x200?text=No+Image'">
                                </c:when>
                                <c:otherwise>
                                    <img src="https://via.placeholder.com/300x200?text=No+Image" alt="No Image" class="product-image">
                                </c:otherwise>
                            </c:choose>
                            <div class="product-name">${product.name}</div>
                            <div class="product-price">$${product.price}</div>
                        </div>
                    </c:forEach>
                </div>
            </c:when>
            <c:otherwise>
                <div class="no-products">
                    <p>No products available at the moment.</p>
                    <p>Please check back later or contact the administrator.</p>
                </div>
            </c:otherwise>
        </c:choose>
    </div>
</body>
</html>
