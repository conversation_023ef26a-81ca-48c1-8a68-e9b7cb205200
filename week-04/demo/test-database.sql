-- Test database connection and verify data
-- Run this script to check if your database is properly set up

-- Check if database exists
SHOW DATABASES LIKE 'shoppingdb';

-- Use the database
USE shoppingdb;

-- Check if product table exists
SHOW TABLES LIKE 'product';

-- Check table structure
DESCRIBE product;

-- Count total products
SELECT COUNT(*) as 'Total Products' FROM product;

-- Show first 5 products
SELECT id, name, price, 
       CASE 
           WHEN image IS NULL THEN 'No Image'
           WHEN image = '' THEN 'Empty Image'
           ELSE 'Has Image'
       END as image_status
FROM product 
ORDER BY id 
LIMIT 5;

-- Check for products with missing data
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN name IS NULL OR name = '' THEN 1 END) as missing_names,
    COUNT(CASE WHEN price IS NULL OR price = 0 THEN 1 END) as missing_prices,
    COUNT(CASE WHEN image IS NULL OR image = '' THEN 1 END) as missing_images
FROM product;

-- Show price range
SELECT 
    MIN(price) as min_price,
    MAX(price) as max_price,
    AVG(price) as avg_price,
    COUNT(*) as total_products
FROM product;
